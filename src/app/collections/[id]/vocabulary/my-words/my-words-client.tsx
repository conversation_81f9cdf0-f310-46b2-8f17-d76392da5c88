'use client';

import { EmptyStateGuidance } from '@/components/onboarding';
import { usePageGuidance } from '@/hooks/use-page-guidance';
import {
	BulkDeleteDialog,
	BulkSelectionControls,
	Button,
	Card,
	CardContent,
	CardHeader,
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	Input,
	Translate,
} from '@/components/ui';
import { useToast } from '@/contexts/toast-context';
import { useTranslation } from '@/contexts';
import { useCollections } from '@/hooks';
import { useDebounce } from '@/lib';
import { WordDetail } from '@/models';
import { Definition, Example, Explain, PartsOfSpeech, Word } from '@prisma/client';
import { BookOpen } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { RecommendedPackages } from '@/components/word-packages';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { SelectableWordCard } from '../../components/selectable-word-card';

interface WordListSkeletonProps {
	count?: number;
}

function WordListSkeleton({ count = 8 }: WordListSkeletonProps) {
	return (
		<div
			className="masonry-grid"
			style={{
				display: 'grid',
				gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
				gap: '1rem',
				alignItems: 'start',
			}}
		>
			{[...Array(count)].map((_, index) => {
				// Randomize skeleton heights for more realistic masonry effect
				const randomHeight = Math.floor(Math.random() * 3) + 2; // 2-4 content blocks
				return (
					<Card key={index} className="flex flex-col" style={{ breakInside: 'avoid' }}>
						<CardHeader className="pb-3">
							<div className="flex items-center justify-between">
								<div className="flex items-center gap-3">
									{/* Word term skeleton */}
									<div className="h-6 w-20 bg-muted rounded animate-pulse" />
								</div>
								{/* Expand button skeleton */}
								<div className="h-8 w-8 bg-muted rounded animate-pulse" />
							</div>
						</CardHeader>
						<CardContent>
							{/* Definition skeleton with random height */}
							<div className="space-y-3">
								{[...Array(randomHeight)].map((_, i) => (
									<div key={i} className="space-y-2">
										<div className="h-4 w-16 bg-muted rounded animate-pulse" />
										<div className="h-4 w-full bg-muted rounded animate-pulse" />
										<div className="h-4 w-3/4 bg-muted rounded animate-pulse" />
									</div>
								))}
								{/* Example skeleton */}
								<div className="space-y-2">
									<div className="h-3 w-full bg-muted rounded animate-pulse" />
									<div className="h-3 w-5/6 bg-muted rounded animate-pulse" />
								</div>
							</div>
						</CardContent>
					</Card>
				);
			})}
		</div>
	);
}

export function MyWordsClient() {
	const { t } = useTranslation();
	const { showSuccess, showError } = useToast();
	const router = useRouter();
	const {
		currentCollection,
		loading,
		currentCollectionWords,
		searchWords,
		fetchCurrentCollectionWords,
		removeWordsFromCurrentCollection,
		refreshCurrentCollection,
		bulkDeleteWordsFromCurrentCollection,
	} = useCollections();

	// State
	const [searchQuery, setSearchQuery] = useState('');
	const collection = currentCollection;
	const [wordActionLoading, setWordActionLoading] = useState<
		Record<string, { removing?: boolean }>
	>({});
	const [wordIdToDelete, setWordIdToDelete] = useState<string | null>(null);
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

	// Bulk selection state
	const [selectionMode, setSelectionMode] = useState(false);
	const [selectedWordIds, setSelectedWordIds] = useState<Set<string>>(new Set());
	const [showBulkDeleteDialog, setShowBulkDeleteDialog] = useState(false);
	const [isBulkDeleting, setIsBulkDeleting] = useState(false);

	const debouncedSearchQuery = useDebounce(searchQuery, 300);
	const isSearching = debouncedSearchQuery.trim().length > 0;

	// Page Guidance hook - must be called at the top level
	usePageGuidance({
		titleKey: 'words.guidance.mywords.title',
		steps: [
			{ key: 'words.guidance.mywords.search' },
			{ key: 'words.guidance.mywords.actions' },
			{ key: 'words.guidance.mywords.delete' },
		],
		defaultOpen: currentCollection?.words.length === 0,
	});

	// Use ref to store stable references and avoid unnecessary refetches
	const collectionIdRef = useRef<string | undefined>(currentCollection?.id);
	const searchWordsRef = useRef(searchWords);
	const fetchCurrentCollectionWordsRef = useRef(fetchCurrentCollectionWords);

	// Update refs when functions change
	searchWordsRef.current = searchWords;
	fetchCurrentCollectionWordsRef.current = fetchCurrentCollectionWords;
	collectionIdRef.current = currentCollection?.id;

	useEffect(() => {
		console.log('🔍 useEffect triggered - debouncedSearchQuery:', debouncedSearchQuery);
		const collectionId = collectionIdRef.current;
		if (collectionId && debouncedSearchQuery.trim()) {
			console.log('🔍 Calling searchWords with query:', debouncedSearchQuery.trim());
			searchWordsRef.current(debouncedSearchQuery.trim());
		} else if (collectionId && !debouncedSearchQuery.trim()) {
			console.log('🔍 Calling fetchCurrentCollectionWords');
			fetchCurrentCollectionWordsRef.current();
		}
	}, [debouncedSearchQuery]); // Only depend on search query, not on functions

	// Transform words data
	const wordsToDisplay = useMemo(() => {
		if (!currentCollection || !collection) return [];

		return (currentCollectionWords || []).map(
			(
				word: Word & {
					definitions?: (Definition & {
						explains?: Explain[];
						examples?: Example[];
						images?: string[];
					})[];
				}
			) =>
				({
					id: word.id,
					term: word.term,
					language: word.language,
					audio_url: word.audio_url ?? null,
					wordNetDataId: null,
					created_at: word.created_at,
					updated_at: word.updated_at,
					definitions: (word.definitions || []).map((def) => ({
						id: def.id,
						word_id: word.id,
						pos: def.pos as PartsOfSpeech[],
						ipa: def.ipa as string,
						images: def.images || [],
						explains: def.explains,
						examples: def.examples,
					})),
					WordNetData: null, // Will be loaded separately if needed
				} as WordDetail)
		);
	}, [currentCollectionWords, collection, currentCollection]);

	// Handlers
	const handleDeleteWord = useCallback((wordId: string) => {
		setWordIdToDelete(wordId);
		setIsDeleteDialogOpen(true);
	}, []);

	const confirmDeleteWord = useCallback(async () => {
		if (!wordIdToDelete || !currentCollection) return;

		setWordActionLoading((prev) => ({ ...prev, [wordIdToDelete]: { removing: true } }));

		try {
			await removeWordsFromCurrentCollection([wordIdToDelete]);
			showSuccess(t('collections.remove_word_success'));
			setIsDeleteDialogOpen(false);
			setWordIdToDelete(null);
			await Promise.all([fetchCurrentCollectionWords(), refreshCurrentCollection()]);
		} catch (error) {
			const err = error instanceof Error ? error : new Error(String(error));
			showError(new Error(t('collections.remove_word_error')));
		} finally {
			setWordActionLoading((prev) => ({ ...prev, [wordIdToDelete]: { removing: false } }));
		}
	}, [
		wordIdToDelete,
		currentCollection,
		removeWordsFromCurrentCollection,
		refreshCurrentCollection,
		showError,
		showSuccess,
		fetchCurrentCollectionWords,
		t,
	]);

	// Bulk selection handlers
	const handleSelectionChange = useCallback((wordId: string, selected: boolean) => {
		setSelectedWordIds((prev) => {
			const newSet = new Set(prev);
			if (selected) {
				newSet.add(wordId);
			} else {
				newSet.delete(wordId);
			}
			return newSet;
		});
	}, []);

	const handleSelectAll = useCallback(() => {
		setSelectedWordIds(new Set(wordsToDisplay.map((word) => word.id)));
	}, [wordsToDisplay]);

	const handleDeselectAll = useCallback(() => {
		setSelectedWordIds(new Set());
	}, []);

	const handleBulkDelete = useCallback(async () => {
		if (!currentCollection || selectedWordIds.size === 0) return;

		setIsBulkDeleting(true);
		try {
			await bulkDeleteWordsFromCurrentCollection(Array.from(selectedWordIds));
			showSuccess(t('words.bulk_delete.success', { count: selectedWordIds.size }));
			setSelectedWordIds(new Set());
			setSelectionMode(false);
			setShowBulkDeleteDialog(false);
		} catch (error) {
			showError(t('words.bulk_delete.error'));
		} finally {
			setIsBulkDeleting(false);
		}
	}, [
		currentCollection,
		selectedWordIds,
		bulkDeleteWordsFromCurrentCollection,
		showSuccess,
		showError,
		t,
	]);

	// Loading state - show skeleton when initially loading words
	if (loading.fetchWords && wordsToDisplay.length === 0 && !searchQuery) {
		return (
			<section className="mt-8">
				<div className="mb-8">
					<h2 className="text-xl font-semibold mb-4">
						<Translate text="collections.words" />
					</h2>
					<Input
						type="text"
						placeholder="Search words…"
						value={searchQuery}
						onChange={(e) => setSearchQuery(e.target.value)}
						disabled
					/>
				</div>
				<div className="mt-8">
					<WordListSkeleton />
				</div>
			</section>
		);
	}

	// Render content based on state
	const renderContent = () => {
		if (!currentCollection) return null;
		if (loading.wordsSearch) return <WordListSkeleton />;

		if (isSearching && wordsToDisplay.length === 0) {
			return (
				<div className="flex justify-center py-8 text-muted-foreground">
					<Translate text="ui.no_results_found" />
				</div>
			);
		}

		if (!isSearching && loading.fetchWords) return <WordListSkeleton />;
		if (!isSearching && wordsToDisplay.length === 0) {
			return (
				<EmptyStateGuidance
					titleKey="words.empty.mywords.title"
					descriptionKey="words.empty.mywords.description"
					actionKey="words.empty.mywords.action"
					actionHref={`/collections/${currentCollection.id}/vocabulary/generate`}
					icon={BookOpen}
				/>
			);
		}

		if (wordsToDisplay.length > 0) {
			return (
				<div
					className="masonry-grid"
					style={{
						display: 'grid',
						gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
						gap: '1rem',
						alignItems: 'start',
					}}
				>
					{wordsToDisplay.map((word) => (
						<div
							key={word.id}
							style={{ breakInside: 'avoid' }}
							className="masonry-grid-item"
						>
							<SelectableWordCard
								word={word}
								onDeleteWord={() => handleDeleteWord(word.id)}
								isDeleting={!!wordActionLoading[word.id]?.removing}
								sourceLanguage={currentCollection.source_language}
								targetLanguage={currentCollection.target_language}
								selectionMode={selectionMode}
								isSelected={selectedWordIds.has(word.id)}
								onSelectionChange={handleSelectionChange}
							/>
						</div>
					))}
				</div>
			);
		}

		return null;
	};

	return (
		<div className="min-h-screen bg-gradient-to-br from-background to-blue-50 dark:from-background dark:to-background">
			<div className="max-w-6xl mx-auto space-y-8 py-8">
				<header className="text-center space-y-4">
					<h1 className="text-4xl font-bold text-primary dark:text-primary">
						<Translate text="words.my_words" />
					</h1>
					<p className="text-muted-foreground dark:text-muted-foreground text-lg">
						<Translate text="words.my_words_description" />
					</p>
				</header>

				{/* Recommended Word Packages - only show when not in selection mode */}
				{currentCollection && !selectionMode && (
					<RecommendedPackages
						collectionId={currentCollection.id}
						source_language={currentCollection.source_language}
						target_language={currentCollection.target_language}
						className="mb-8"
					/>
				)}

				<section className="mt-8">
					<div className="mb-8">
						<div className="flex items-center justify-between mb-4">
							<h2 className="text-xl font-semibold">
								<Translate text="collections.words" />
							</h2>
							{wordsToDisplay.length > 0 && (
								<Button
									variant={selectionMode ? 'default' : 'outline'}
									size="sm"
									onClick={() => {
										console.log(
											'🔄 Toggling selectionMode from',
											selectionMode,
											'to',
											!selectionMode
										);
										setSelectionMode(!selectionMode);
									}}
									disabled={isBulkDeleting}
								>
									{selectionMode ? (
										<Translate text="words.bulk_delete.cancel_selection" />
									) : (
										<Translate text="ui.select" />
									)}
								</Button>
							)}
						</div>
						<Input
							type="text"
							placeholder="Search words…"
							value={searchQuery}
							onChange={(e) => setSearchQuery(e.target.value)}
							disabled={selectionMode}
						/>
					</div>

					{/* Bulk selection controls - always show when in selection mode */}
					{selectionMode && (
						<BulkSelectionControls
							selectedCount={selectedWordIds.size}
							totalCount={wordsToDisplay.length}
							onSelectAll={handleSelectAll}
							onDeselectAll={handleDeselectAll}
							onDeleteSelected={() => setShowBulkDeleteDialog(true)}
							isDeleting={isBulkDeleting}
							className="mb-6"
						/>
					)}

					<div className="mt-8">{renderContent()}</div>
				</section>
			</div>

			<Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>
							<Translate text="collections.remove_word" />
						</DialogTitle>
						<DialogDescription>
							<Translate text="collections.remove_word_confirm" />
						</DialogDescription>
					</DialogHeader>
					<DialogFooter>
						<Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
							<Translate text="ui.cancel" />
						</Button>
						<Button
							variant="destructive"
							onClick={confirmDeleteWord}
							disabled={
								!!(wordIdToDelete && wordActionLoading[wordIdToDelete]?.removing)
							}
						>
							<Translate text="ui.remove" />
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Bulk delete confirmation dialog */}
			<BulkDeleteDialog
				open={showBulkDeleteDialog}
				onOpenChange={setShowBulkDeleteDialog}
				selectedCount={selectedWordIds.size}
				onConfirm={handleBulkDelete}
				isDeleting={isBulkDeleting}
			/>
		</div>
	);
}
